@page
@model RazeWinComTr.Areas.MyAccount.Pages.WalletModel
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<SharedResource> Localizer
@{
    ViewData["Title"] = Localizer["My Wallet"];
}

@section Styles {
    <!-- DataTables CSS from local files -->
    <link rel="stylesheet" href="/plugins/datatables/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="/plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
    <link rel="stylesheet" href="/plugins/datatables-buttons/css/buttons.bootstrap4.min.css">

    <style>
        /* Custom styles for better readability */
        body {
            color: #333 !important;
        }

        /* Custom styles for the wallet table */
        #transactionTable {
            color: #333;
            background-color: #fff;
            border-collapse: collapse;
            width: 100%;
        }

        #transactionTable thead th {
            color: #fff !important;
            background-color: #343a40 !important;
            border-color: #454d55 !important;
            font-weight: bold;
            padding: 10px;
            text-align: left;
        }

        #transactionTable tbody td {
            color: #333 !important;
            background-color: #fff !important;
            padding: 8px;
            border: 1px solid #dee2e6;
        }

        #transactionTable tfoot th {
            color: #fff !important;
            background-color: #343a40 !important;
            border-color: #454d55 !important;
            font-weight: bold;
            padding: 10px;
        }

        /* Striped rows */
        #transactionTable tbody tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.05) !important;
        }

        /* Hover effect */
        #transactionTable tbody tr:hover {
            color: #333 !important;
            background-color: rgba(0, 0, 0, 0.075) !important;
        }

        /* DataTables specific styling */
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter,
        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_processing,
        .dataTables_wrapper .dataTables_paginate {
            color: #333 !important;
            margin-bottom: 10px;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            color: #333 !important;
            background-color: #f8f9fa !important;
            border: 1px solid #dee2e6 !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current,
        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            color: #fff !important;
            background-color: #007bff !important;
            border-color: #007bff !important;
        }

        /* Fix for DataTables buttons */
        .dt-buttons .btn {
            color: #333 !important;
            background-color: #f8f9fa !important;
            border: 1px solid #dee2e6 !important;
            margin-right: 5px;
        }

        .dt-buttons .btn:hover {
            color: #333 !important;
            background-color: #e2e6ea !important;
            border-color: #dae0e5 !important;
        }

    </style>
}
<!-- Content Header -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@Localizer["My Wallet"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/MyAccount/Dashboard">@Localizer["Home"]</a></li>
                    <li class="breadcrumb-item active">@Localizer["My Wallet"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- RZW Balance Card (if user has RZW) -->
@if (Model.HasRzwBalance && Model.RzwBalance != null)
{
    <div class="card myaccount-card mb-4" style="border-left: 4px solid #667eea;">
        <div class="card-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <h5 class="m-0">
                <i class="fas fa-gem mr-2"></i>@Localizer["RZW Savings"]
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- Available Balance -->
                <div class="col-md-3 mb-3">
                    <div class="text-center p-3 bg-light rounded">
                        <div class="text-success mb-2">
                            <i class="fas fa-wallet" style="font-size: 24px;"></i>
                        </div>
                        <h6 class="text-muted mb-1">@Localizer["Available Balance"]</h6>
                        <h4 class="mb-0 text-success">@Model.RzwBalance.AvailableRzw.ToString("N8", new System.Globalization.CultureInfo("en-US"))</h4>
                        <small class="text-muted">RZW</small>
                    </div>
                </div>

                <!-- Locked Balance -->
                <div class="col-md-3 mb-3">
                    <div class="text-center p-3 bg-light rounded">
                        <div class="text-warning mb-2">
                            <i class="fas fa-lock" style="font-size: 24px;"></i>
                        </div>
                        <h6 class="text-muted mb-1">@Localizer["Locked Balance"]</h6>
                        <h4 class="mb-0 text-warning">@Model.RzwBalance.LockedRzw.ToString("N8", new System.Globalization.CultureInfo("en-US"))</h4>
                        <small class="text-muted">RZW</small>
                    </div>
                </div>

                <!-- Total Balance -->
                <div class="col-md-3 mb-3">
                    <div class="text-center p-3 bg-light rounded">
                        <div class="text-primary mb-2">
                            <i class="fas fa-chart-line" style="font-size: 24px;"></i>
                        </div>
                        <h6 class="text-muted mb-1">@Localizer["Total Balance"]</h6>
                        <h4 class="mb-0 text-primary">@Model.RzwBalance.TotalRzw.ToString("N8", new System.Globalization.CultureInfo("en-US"))</h4>
                        <small class="text-muted">RZW</small>
                    </div>
                </div>

                <!-- Locked in Savings -->
                <div class="col-md-3 mb-3">
                    <div class="text-center p-3 bg-light rounded">
                        <div class="text-info mb-2">
                            <i class="fas fa-piggy-bank" style="font-size: 24px;"></i>
                        </div>
                        <h6 class="text-muted mb-1">@Localizer["Locked In Savings"]</h6>
                        <h4 class="mb-0 text-info">@Model.RzwBalance.LockedInSavings.ToString("N8", new System.Globalization.CultureInfo("en-US"))</h4>
                        <small class="text-muted">RZW</small>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-3">
                <div class="col-md-12">
                    <div class="d-flex justify-content-center flex-wrap gap-2">
                        @if (Model.RzwBalance.AvailableRzw > 0)
                        {
                            <a href="/MyAccount/RzwSavings/Create" class="btn btn-success">
                                <i class="fas fa-plus mr-2"></i>@Localizer["Create New Savings Account"]
                            </a>
                        }
                        @if (Model.HasActiveSavings)
                        {
                            <a href="/MyAccount/RzwSavings" class="btn btn-info">
                                <i class="fas fa-eye mr-2"></i>@Localizer["View Savings Details"]
                            </a>
                        }
                        <a href="/MyAccount/RzwSavings/InterestHistory" class="btn btn-outline-primary">
                            <i class="fas fa-history mr-2"></i>@Localizer["Interest History"]
                        </a>
                    </div>
                </div>
            </div>

            <!-- Active Savings Summary -->
            @if (Model.HasActiveSavings)
            {
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h6 class="mb-3">@Localizer["Active Savings Accounts"] (@Model.ActiveSavingsAccounts.Count)</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>@Localizer["Plan Name"]</th>
                                        <th>@Localizer["Amount"]</th>
                                        <th>@Localizer["Interest Rate"]</th>
                                        <th>@Localizer["Maturity Date"]</th>
                                        <th>@Localizer["Days Remaining"]</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var account in Model.ActiveSavingsAccounts.Take(3))
                                    {
                                        var daysRemaining = Math.Max(0, (account.MaturityDate - DateTime.UtcNow).Days);
                                        <tr>
                                            <td>@account.Plan?.Name</td>
                                            <td>@account.RzwAmount.ToString("N8", new System.Globalization.CultureInfo("en-US")) RZW</td>
                                            <td>@((account.InterestRate * 100).ToString("N2"))%</td>
                                            <td>@account.MaturityDate.ToLocalTime().ToString("dd.MM.yyyy")</td>
                                            <td>
                                                @if (daysRemaining > 0)
                                                {
                                                    <span class="badge badge-warning">@daysRemaining @Localizer["Days Remaining"]</span>
                                                }
                                                else
                                                {
                                                    <span class="badge badge-success">@Localizer["Matured"]</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        @if (Model.ActiveSavingsAccounts.Count > 3)
                        {
                            <div class="text-center mt-2">
                                <a href="/MyAccount/RzwSavings" class="btn btn-sm btn-outline-primary">
                                    @Localizer["View All"] (@Model.ActiveSavingsAccounts.Count)
                                </a>
                            </div>
                        }
                    </div>
                </div>
            }
        </div>
    </div>
}

<!-- Wallet Balances -->
<div class="card myaccount-card mb-4">
    <div class="card-header">
        <h5 class="m-0">@Localizer["My Balances"]</h5>
    </div>
    <div class="card-body">
        <div class="row">
            @foreach (var wallet in Model.Wallets)
            {
                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="card-title mb-0">@wallet.CoinName (@wallet.CoinCode)</h5>
                        </div>
                        <div class="card-body text-center">
                            <h2 class="mb-3">@wallet.Balance.ToString("N8", new System.Globalization.CultureInfo("en-US"))</h2>
                            <a href="/market" class="btn btn-primary">
                                <i class="fas fa-exchange-alt mr-2"></i> @Localizer["Trade"]
                            </a>
                        </div>
                        <div class="card-footer bg-white">
                            <small class="text-muted">
                                <i class="fas fa-clock mr-1"></i> @Localizer["Last updated"]:
                                @(wallet.ModifiedDate?.ToLocalTime().ToString("dd.MM.yyyy HH:mm") ?? wallet.CreatedDate.ToLocalTime().ToString("dd.MM.yyyy HH:mm"))
                            </small>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Transaction History -->
<div class="card myaccount-card">
    <div class="card-header">
        <h5 class="m-0">@Localizer["Transaction History"]</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table id="transactionTable" class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>@Localizer["Date"]</th>
                        <th>@Localizer["Type"]</th>
                        <th>@Localizer["Coin"]</th>
                        <th>@Localizer["Amount"]</th>
                        <th>@Localizer["Rate"]</th>
                        <th>@Localizer["Total"]</th>
                        <th>@Localizer["Previous Balance"]</th>
                        <th>@Localizer["New Balance"]</th>
                        <th>@Localizer["Previous Wallet Balance"]</th>
                        <th>@Localizer["New Wallet Balance"]</th>
                        <th>@Localizer["Description"]</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.Trades)
                    {
                        <tr>
                            <td>@item.CreatedDate.ToLocalTime().ToString("dd.MM.yyyy HH:mm")</td>
                            <td>
                                @if (item.Type == RazeWinComTr.Areas.Admin.DbModel.TradeType.Buy)
                                {
                                    <span class="badge badge-success">@Localizer["Purchase"]</span>
                                }
                                else if (item.Type == RazeWinComTr.Areas.Admin.DbModel.TradeType.Sell)
                                {
                                    <span class="badge badge-danger">@Localizer["Sale"]</span>
                                }
                                else if (item.Type == RazeWinComTr.Areas.Admin.DbModel.TradeType.PackageBonus)
                                {
                                    <span class="badge badge-info">@Localizer["Package Bonus"]</span>
                                }
                                else if (item.Type == RazeWinComTr.Areas.Admin.DbModel.TradeType.ReferralReward)
                                {
                                    <span class="badge badge-warning">@Localizer["Referral Reward"]</span>
                                }
                                else if (item.Type == RazeWinComTr.Areas.Admin.DbModel.TradeType.RzwSavingsDeposit)
                                {
                                    <span class="badge badge-primary">@Localizer["Savings Deposit"]</span>
                                }
                                else if (item.Type == RazeWinComTr.Areas.Admin.DbModel.TradeType.RzwSavingsWithdrawal)
                                {
                                    <span class="badge badge-secondary">@Localizer["Savings Withdrawal"]</span>
                                }
                                else if (item.Type == RazeWinComTr.Areas.Admin.DbModel.TradeType.RzwSavingsInterest)
                                {
                                    <span class="badge badge-success">@Localizer["Savings Interest"]</span>
                                }
                                else if (item.Type == RazeWinComTr.Areas.Admin.DbModel.TradeType.RzwSavingsEarlyWithdrawal)
                                {
                                    <span class="badge badge-warning">@Localizer["Savings Early Withdrawal"]</span>
                                }
                                else if (item.Type == RazeWinComTr.Areas.Admin.DbModel.TradeType.RzwSavingsMaturity)
                                {
                                    <span class="badge badge-info">@Localizer["Savings Maturity"]</span>
                                }
                                else
                                {
                                    <span class="badge badge-secondary">@item.Type.ToString()</span>
                                }
                            </td>
                            <td>@item.CoinCode</td>
                            <td class="text-right">@item.CoinAmount.ToString("N8")</td>
                            <td class="text-right">@item.CoinRate.ToString("N2") ₺</td>
                            <td class="text-right">@item.TryAmount.ToString("N2") ₺</td>
                            <td class="text-right">@item.PreviousBalance.ToString("N2") ₺</td>
                            <td class="text-right">@item.NewBalance.ToString("N2") ₺</td>
                            <td class="text-right">@item.PreviousWalletBalance.ToString("N8")</td>
                            <td class="text-right">@item.NewWalletBalance.ToString("N8")</td>
                            <td>
                                @if (item.ReferralRewardId.HasValue)
                                {
                                    <div class="text-muted small">
                                        @item.GetReferralRewardDescription()
                                    </div>
                                }
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

@section Scripts {
    <!-- jQuery first, then Bootstrap, then other libraries -->
    <script src="/plugins/jquery/jquery.min.js"></script>
    <script src="/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- DataTables & Plugins from local files -->
    <script src="/plugins/datatables/js/jquery.dataTables.min.js"></script>
    <script src="/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="/plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
    <script src="/plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
    <script src="/plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
    <script src="/plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
    <script src="/plugins/jszip/jszip.min.js"></script>
    <script src="/plugins/pdfmake/pdfmake.min.js"></script>
    <script src="/plugins/pdfmake/vfs_fonts.js"></script>
    <script src="/plugins/datatables-buttons/js/buttons.html5.min.js"></script>
    <script src="/plugins/datatables-buttons/js/buttons.print.min.js"></script>
    <script src="/plugins/datatables-buttons/js/buttons.colVis.min.js"></script>

    <script>
        // Initialize translations object
        window.t = window.t || {};

        // Add translations
        window.t["Search"] = "@Localizer["Search"]";
        window.t["First"] = "@Localizer["First"]";
        window.t["Last"] = "@Localizer["Last"]";
        window.t["Next"] = "@Localizer["Next"]";
        window.t["Previous"] = "@Localizer["Previous"]";
        window.t["Showing _START_ to _END_ of _TOTAL_ entries"] = "@Localizer["Showing _START_ to _END_ of _TOTAL_ entries"]";
        window.t["No entries available"] = "@Localizer["No entries available"]";
        window.t["(filtered from _MAX_ total records)"] = "@Localizer["(filtered from _MAX_ total records)"]";
        window.t["No matching records found"] = "@Localizer["No matching records found"]";

        // Add date sorting plugin for European date format (dd.MM.yyyy HH:mm)
        $.fn.dataTable.ext.type.order['date-eu-pre'] = function(data) {
            if (!data) {
                return 0;
            }

            // Extract date parts from format dd.MM.yyyy HH:mm
            var parts = data.split(' ');
            var dateParts = parts[0].split('.');
            var timeParts = parts[1].split(':');

            // Create a date object using the parts
            var day = parseInt(dateParts[0], 10);
            var month = parseInt(dateParts[1], 10) - 1; // Months are 0-based in JS
            var year = parseInt(dateParts[2], 10);
            var hour = parseInt(timeParts[0], 10);
            var minute = parseInt(timeParts[1], 10);

            // Create a sortable value (timestamp)
            var date = new Date(year, month, day, hour, minute, 0);
            return date.getTime();
        };

        $(document).ready(function() {
            try {
                console.log("DataTable initialization starting...");
                var table = $("#transactionTable").DataTable({
                    "responsive": true,
                    "lengthChange": false,
                    "autoWidth": false,
                    "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"],
                    "order": [[0, "desc"]],
                    "columnDefs": [
                        {
                            "targets": 0,
                            "type": "date-eu"
                        }
                    ],
                    "initComplete": function() {
                        // Force initial sort
                        this.api().order([0, 'desc']).draw();
                    },
                    "language": {
                        "search": window.t["Search"],
                        "paginate": {
                            "first": window.t["First"],
                            "last": window.t["Last"],
                            "next": window.t["Next"],
                            "previous": window.t["Previous"]
                        },
                        "info": window.t["Showing _START_ to _END_ of _TOTAL_ entries"],
                        "infoEmpty": window.t["No entries available"],
                        "infoFiltered": window.t["(filtered from _MAX_ total records)"],
                        "zeroRecords": window.t["No matching records found"]
                    }
                });

                console.log("DataTable initialized successfully");

                // Add buttons to container
                table.buttons().container().appendTo('#transactionTable_wrapper .col-md-6:eq(0)');

                // Apply custom styling to DataTables elements
                $('.dataTables_filter input').addClass('form-control').css({
                    'display': 'inline-block',
                    'width': 'auto',
                    'margin-left': '10px'
                });

                $('.dataTables_length select').addClass('form-control').css({
                    'display': 'inline-block',
                    'width': 'auto',
                    'margin-left': '10px',
                    'margin-right': '10px'
                });
            } catch (e) {
                console.error("Error initializing DataTable:", e);
            }
        });
    </script>
}
